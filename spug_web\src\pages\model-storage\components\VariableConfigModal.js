import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Button,
  Space,
  Card,
  Row,
  Col,
  Typography,
  Tag,
  message,
  Alert
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  SettingOutlined,
  DragOutlined
} from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import http from '../../../libs/http';
import styles from './VariableConfigModal.module.less';
import SimpleRichTextEditor from './SimpleRichTextEditor';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

export default function VariableConfigModal({ 
  visible, 
  onCancel, 
  onOk, 
  template,
  variables = []
}) {
  const [loading, setLoading] = useState(false);
  const [variableList, setVariableList] = useState([]);
  const [parsedVariables, setParsedVariables] = useState([]);

  useEffect(() => {
    if (visible && template) {
      initializeVariables();
      parseTemplateVariables();
    }
  }, [visible, template, initializeVariables, parseTemplateVariables]);

  const initializeVariables = () => {
    if (variables && variables.length > 0) {
      setVariableList(variables.map((v, index) => ({ ...v, key: index })));
    } else {
      setVariableList([]);
    }
  };

  const fetchGpuOptions = async () => {
    try {
      const res = await http.get('/api/model-storage/gpus/');
      if (!res.error && res.data) {
        const options = res.data.map(gpu => ({
          label: `${gpu.name} (${gpu.vendor})`,
          value: gpu.name
        }));
        setGpuOptions(options);
      }
    } catch (error) {
      // 获取GPU选项失败时静默处理
    }
  };

  const fetchVendorOptions = async () => {
    try {
      const res = await http.get('/api/model-storage/gpus/');
      if (!res.error && res.data) {
        const vendors = [...new Set(res.data.map(gpu => gpu.vendor))];
        const options = vendors.map(vendor => ({
          label: vendor,
          value: vendor
        }));
        setVendorOptions(options);
      }
    } catch (error) {
      // 获取厂商选项失败时静默处理
    }
  };

  const parseTemplateVariables = async () => {
    if (!template || !template.template_file) return;

    try {
      const res = await http.post('/api/model-storage/template-variables/parse/', {
        template_file_path: template.template_file
      });
      
      if (!res.error && res.data) {
        setParsedVariables(res.data.variables || []);
      }
    } catch (error) {
      // 解析模板变量失败时静默处理
    }
  };

  const handleAddVariable = () => {
    const newVariable = {
      key: Date.now(),
      variable_name: '',
      display_name: '',
      variable_type: 'text',
      default_value: '',
      options: [],
      is_required: true,
      validation_rule: '',
      sort_order: variableList.length,
      group_name: '基本信息',
      help_text: '',
      placeholder: ''
    };
    setVariableList([...variableList, newVariable]);
  };

  const handleDeleteVariable = (key) => {
    setVariableList(variableList.filter(v => v.key !== key));
  };

  const handleVariableChange = (key, field, value) => {
    setVariableList(variableList.map(v => 
      v.key === key ? { ...v, [field]: value } : v
    ));
  };

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const items = Array.from(variableList);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // 更新排序
    const updatedItems = items.map((item, index) => ({
      ...item,
      sort_order: index
    }));

    setVariableList(updatedItems);
  };

  const handleImportParsedVariables = () => {
    if (parsedVariables.length === 0) {
      message.warning('没有解析到模板变量');
      return;
    }

    const importedVariables = parsedVariables.map((v, index) => ({
      ...v,
      key: Date.now() + index,
      sort_order: variableList.length + index
    }));

    setVariableList([...variableList, ...importedVariables]);
    message.success(`成功导入 ${parsedVariables.length} 个变量`);
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      // 验证变量配置
      const errors = [];
      variableList.forEach((v, index) => {
        if (!v.variable_name) {
          errors.push(`第 ${index + 1} 个变量缺少变量名`);
        }
        if (!v.display_name) {
          errors.push(`第 ${index + 1} 个变量缺少显示名称`);
        }
      });

      if (errors.length > 0) {
        message.error(errors.join('; '));
        return;
      }

      // 调用父组件的回调
      if (onOk) {
        await onOk(variableList);
      }
      
      message.success('变量配置保存成功');
    } catch (error) {
      message.error('保存变量配置失败：' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const getVariableTypeOptions = () => [
    { label: '文本', value: 'text' },
    { label: '多行文本', value: 'textarea' },
    { label: '富文本', value: 'richtext' },
    { label: '选择', value: 'select' },
    { label: '多选', value: 'multiselect' },
    { label: '日期', value: 'date' },
    { label: '数字', value: 'number' },
    { label: 'GPU型号', value: 'gpu_model' },
    { label: '厂商', value: 'vendor' }
  ];

  const renderVariableItem = (variable, index) => (
    <Draggable key={variable.key} draggableId={String(variable.key)} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={`${styles.variableItem} ${snapshot.isDragging ? styles.dragging : ''}`}
        >
          <Card
            size="small"
            title={
              <Space>
                <div {...provided.dragHandleProps}>
                  <DragOutlined style={{ cursor: 'grab' }} />
                </div>
                <Text strong>变量 {index + 1}</Text>
                <Tag color="blue">{variable.variable_type}</Tag>
              </Space>
            }
            extra={
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteVariable(variable.key)}
                size="small"
              />
            }
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div className={styles.formItem}>
                  <label>变量名 <Text type="danger">*</Text></label>
                  <Input
                    value={variable.variable_name}
                    onChange={(e) => handleVariableChange(variable.key, 'variable_name', e.target.value)}
                    placeholder="如：GPU、厂商、BIOS设置正文"
                  />
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.formItem}>
                  <label>显示名称 <Text type="danger">*</Text></label>
                  <Input
                    value={variable.display_name}
                    onChange={(e) => handleVariableChange(variable.key, 'display_name', e.target.value)}
                    placeholder="如：厂商名称"
                  />
                </div>
              </Col>
              <Col span={8}>
                <div className={styles.formItem}>
                  <label>变量类型</label>
                  <Select
                    value={variable.variable_type}
                    onChange={(value) => handleVariableChange(variable.key, 'variable_type', value)}
                    style={{ width: '100%' }}
                  >
                    {getVariableTypeOptions().map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </div>
              </Col>
              <Col span={8}>
                <div className={styles.formItem}>
                  <label>分组</label>
                  <Input
                    value={variable.group_name}
                    onChange={(e) => handleVariableChange(variable.key, 'group_name', e.target.value)}
                    placeholder="如：基本信息"
                  />
                </div>
              </Col>
              <Col span={8}>
                <div className={styles.formItem}>
                  <label>是否必填</label>
                  <Switch
                    checked={variable.is_required}
                    onChange={(checked) => handleVariableChange(variable.key, 'is_required', checked)}
                  />
                </div>
              </Col>
              <Col span={variable.variable_type === 'richtext' ? 24 : 12}>
                <div className={styles.formItem}>
                  <label>默认值</label>
                  {variable.variable_type === 'richtext' ? (
                    <SimpleRichTextEditor
                      value={variable.default_value}
                      onChange={(value) => handleVariableChange(variable.key, 'default_value', value)}
                      placeholder="请输入富文本内容..."
                      height={200}
                    />
                  ) : variable.variable_type === 'textarea' ? (
                    <TextArea
                      value={variable.default_value}
                      onChange={(e) => handleVariableChange(variable.key, 'default_value', e.target.value)}
                      placeholder="默认值"
                      rows={3}
                    />
                  ) : (
                    <Input
                      value={variable.default_value}
                      onChange={(e) => handleVariableChange(variable.key, 'default_value', e.target.value)}
                      placeholder="默认值"
                    />
                  )}
                </div>
              </Col>
              {variable.variable_type !== 'richtext' && (
                <Col span={12}>
                  <div className={styles.formItem}>
                    <label>占位符</label>
                    <Input
                      value={variable.placeholder}
                      onChange={(e) => handleVariableChange(variable.key, 'placeholder', e.target.value)}
                      placeholder="输入提示"
                    />
                  </div>
                </Col>
              )}
              {(variable.variable_type === 'select' || variable.variable_type === 'multiselect') && (
                <Col span={24}>
                  <div className={styles.formItem}>
                    <label>选项配置</label>
                    <Select
                      mode="tags"
                      value={variable.options}
                      onChange={(value) => handleVariableChange(variable.key, 'options', value)}
                      placeholder="输入选项，按回车添加"
                      style={{ width: '100%' }}
                    />
                  </div>
                </Col>
              )}
              <Col span={24}>
                <div className={styles.formItem}>
                  <label>帮助文本</label>
                  <TextArea
                    value={variable.help_text}
                    onChange={(e) => handleVariableChange(variable.key, 'help_text', e.target.value)}
                    placeholder="帮助用户理解这个变量的用途"
                    rows={2}
                  />
                </div>
              </Col>
            </Row>
          </Card>
        </div>
      )}
    </Draggable>
  );

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          <span>配置模板变量</span>
          {template && <Tag color="blue">{template.name}</Tag>}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      width={1000}
      confirmLoading={loading}
      okText="保存配置"
      cancelText="取消"
      destroyOnClose
    >
      <div className={styles.container}>
        {parsedVariables.length > 0 && (
          <Alert
            message="检测到模板变量"
            description={
              <div>
                <Text>从模板文件中解析到 {parsedVariables.length} 个变量：</Text>
                <div style={{ marginTop: 8 }}>
                  {parsedVariables.map(v => (
                    <Tag key={v.variable_name} color="green" style={{ margin: '2px' }}>
                      {v.variable_name}
                    </Tag>
                  ))}
                </div>
                <Button 
                  type="link" 
                  size="small" 
                  onClick={handleImportParsedVariables}
                  style={{ padding: 0, marginTop: 8 }}
                >
                  导入这些变量
                </Button>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        <div className={styles.header}>
          <Title level={4}>变量配置</Title>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={handleAddVariable}
          >
            添加变量
          </Button>
        </div>

        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="variables">
            {(provided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className={styles.variableList}
              >
                {variableList.map((variable, index) => 
                  renderVariableItem(variable, index)
                )}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>

        {variableList.length === 0 && (
          <div className={styles.emptyState}>
            <Text type="secondary">
              暂无变量配置，点击"添加变量"开始配置
            </Text>
          </div>
        )}
      </div>
    </Modal>
  );
}
